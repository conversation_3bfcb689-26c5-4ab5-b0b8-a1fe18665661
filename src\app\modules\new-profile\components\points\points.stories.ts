import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { PointsComponent } from './points.component';
import { TranslateModule } from '@ngx-translate/core';

const meta: Meta<PointsComponent> = {
  title: 'Pages/Points',
  component: PointsComponent,
  decorators: [
    moduleMetadata({
      imports: [PointsComponent],
    }),
  ],
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<PointsComponent>;

export const Default: Story = {
  render: (args) => ({ props: args }),
};
