import { DatePipe, NgIf } from '@angular/common';
import { Component, Input } from '@angular/core';
import { NtranslatePipe } from '../../pipes/ntranslate.pipe';
import { SharedModule } from "../../shared.module";
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';
import { SharedBtnComponent } from "../shared-btn/shared-btn.component";

@Component({
  selector: 'app-points-status-widget',
  standalone: true,
  imports: [DatePipe, NgIf, NtranslatePipe, SvgIconsComponent, SharedBtnComponent],
  templateUrl: './points-status-widget.component.html',
  styleUrl: './points-status-widget.component.scss'
})
export class PointsStatusWidgetComponent {

  @Input() current = 22;
  @Input() total = 100;
  @Input() pointsRequired = 10;
  @Input() expiresAt: Date | string = new Date();
  @Input() label: string | null = null;
  @Input() iconName: string | null = null;

  get remainingAfterPurchase(): number {
    return this.total - this.pointsRequired;
  }

  get cannotBuy(): boolean {
    return this.pointsRequired > (this.total - this.current);
  }

  get percent(): number {
    const p = (this.current / Math.max(1, this.total)) * 100;
    return Math.max(0, Math.min(100, Math.round(p * 10) / 10));
  }

  get percentwithRequired(): number {
    const p = ((this.current + this.pointsRequired) / Math.max(1, this.total)) * 100;
    return Math.max(0, Math.min(100, Math.round(p * 10) / 10));
  }
}
