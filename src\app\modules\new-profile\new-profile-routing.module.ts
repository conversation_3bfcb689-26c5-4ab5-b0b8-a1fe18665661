import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { NewRatingsComponent } from "./pages/new-ratings/new-ratings.component";
import { NewMyProfileComponent } from "./pages/new-my-profile/new-my-profile.component";
import { NewProfileLayoutComponent } from "./new-profile-layout/new-profile-layout.component";
import { PointsComponent } from "./components/points/points.component";

const routes: Routes = [
  {
    path: "",
    component: NewProfileLayoutComponent,
    children: [
      { path: "", component: NewMyProfileComponent },
      { path: "ratings", component: NewRatingsComponent },
      { path: "points", component: PointsComponent },

    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class NewProfileRoutingModule {}