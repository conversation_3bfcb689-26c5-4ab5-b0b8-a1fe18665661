import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { LanguageButtonComponent } from '@shared/components/language-button/language-button.component';
import { SuccessIconComponent } from '@shared/components/success-icon/success-icon.component';
import { NtranslatePipe } from '@shared/pipes/ntranslate.pipe';
import { SharedModule } from '@shared/shared.module';
import { NgxOtpInputComponent } from 'ngx-otp-input';
import { PrimengModuleModule } from 'src/app/modules/shared-modules/primeng-module.module';

@NgModule({
  declarations: [

  ],
  imports: [
    CommonModule,
    RouterModule.forChild([
      {
        path: 'register',
        loadChildren: () =>
          import('src/app/authentication/user-registeration/user-registeration.module').then(
            (m) => m.UserRegisterationModule
          ),
      },
      {
        path: 'register-operator',
        loadChildren: () =>
          import('src/app/authentication/operation-registeration/operation-registeration.module').then(
            (m) => m.OperationRegisterationModule
          ),
      },

      {
        path: 'register-partner',
        loadChildren: () =>
          import('src/app/authentication/partner-registeration/partner-registeration.module').then(
            (m) => m.PartnerRegisterationModule
          ),
      },

      {
        path: 'profile',
        loadChildren: () =>
          import('src/app/modules/new-profile/new-profile.module').then(
            (m) => m.NewProfileModule
          ),
      },
      {
        path: 'emailconfirmation',
        loadChildren: () =>
          import('src/app/authentication/emailconfirmation/emailconfirmation.module').then(
            (m) => m.EmailconfirmationModule
          ),
      },



    ]),
    ReactiveFormsModule,
    FormsModule,
    NgxOtpInputComponent,
    PrimengModuleModule,
    SuccessIconComponent,
    LanguageButtonComponent,
    SharedModule,
    NtranslatePipe,

  ],
})
export class AuthenticationModule { }
