import { CommonModule } from '@angular/common';
import { Component} from '@angular/core';
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";
import { PointsStatusWidgetComponent } from "@src/app/shared/components/points-status-widget/points-status-widget.component";
@Component({
  selector: 'app-points',
  standalone: true,
  imports: [CommonModule, DynamicHeaderComponent, PointsStatusWidgetComponent],
  templateUrl: './points.component.html',
  styleUrl: './points.component.scss'
})
export class PointsComponent {
  
}
